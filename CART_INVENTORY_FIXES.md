# Sửa lỗi hiển thị số lượng tồn kho trong giỏ hàng

## Vấn đề ban đầu
- <PERSON><PERSON> lượng tồn kho (`productQuantity`) trong giỏ hàng không được cập nhật theo thời gian thực
- Người dùng có thể thêm sản phẩm vào giỏ hàng vượt quá số lượng tồn kho
- Không có cảnh báo khi sản phẩm sắp hết hàng hoặc đã hết hàng

## Các thay đổi đã thực hiện

### 1. Backend (cartController.js)
- **Import productModel**: Thêm import để truy cập thông tin sản phẩm
- **Cập nhật getCart()**: L<PERSON>y thông tin tồn kho mới nhất từ database khi trả về giỏ hàng
- **Cập nhật addToCart()**: 
  - <PERSON><PERSON><PERSON> tra tồn kho trước khi thêm sản phẩm
  - <PERSON><PERSON>m tra tổng số lượng khi cộng dồn sản phẩm đã có trong giỏ
  - Trả về lỗi nếu vượt quá tồn kho
- **Cập nhật updateCartQuantity()**: Kiểm tra tồn kho khi cập nhật số lượng

### 2. Backend (Cart.js Model)
- **Thêm trường productQuantity**: Lưu thông tin tồn kho trong schema giỏ hàng

### 3. Frontend (Cart.jsx)
- **Cải thiện handleUpdateQuantity()**: Kiểm tra giới hạn tồn kho trước khi cập nhật
- **Cập nhật UI nút tăng/giảm**: 
  - Disable nút "+" khi đạt giới hạn tồn kho
  - Thêm tooltip giải thích lý do disable
- **Hiển thị trạng thái tồn kho**:
  - Cảnh báo khi còn ít hàng (≤ 5 sản phẩm)
  - Hiển thị "Hết hàng" khi quantity = 0
  - Hiển thị "Không khả dụng" khi sản phẩm bị xóa khỏi database

### 4. Frontend (Cart.css)
- **Thêm styles cho nút disabled**: Làm rõ trạng thái không thể click
- **Thêm styles cho trạng thái tồn kho**:
  - `.low-stock`: Màu vàng cảnh báo
  - `.out-of-stock`: Màu đỏ nguy hiểm

### 5. Frontend (CartContext.jsx)
- **Xử lý lỗi từ backend**: Hiển thị thông báo lỗi khi vượt quá tồn kho
- **Đồng bộ lại giỏ hàng**: Fetch lại từ server khi có lỗi để đảm bảo consistency

## Tính năng mới
1. **Kiểm tra tồn kho thời gian thực**: Mỗi lần lấy giỏ hàng sẽ cập nhật thông tin tồn kho mới nhất
2. **Cảnh báo tồn kho thấp**: Hiển thị cảnh báo khi sản phẩm còn ít (≤ 5)
3. **Ngăn chặn thêm quá tồn kho**: Kiểm tra ở cả frontend và backend
4. **Xử lý sản phẩm không khả dụng**: Hiển thị thông báo khi sản phẩm bị xóa
5. **UI/UX cải thiện**: Nút disable, tooltip, màu sắc cảnh báo

## Cách test
1. Thêm sản phẩm vào giỏ hàng
2. Thử tăng số lượng vượt quá tồn kho → Sẽ bị chặn
3. Kiểm tra hiển thị cảnh báo khi tồn kho thấp
4. Xóa sản phẩm khỏi database và reload giỏ hàng → Hiển thị "không khả dụng"

## Lưu ý
- Cần restart cả backend và frontend để áp dụng thay đổi
- Database cần có trường `quantity` trong collection `products`
- Các thay đổi tương thích ngược với dữ liệu cũ
