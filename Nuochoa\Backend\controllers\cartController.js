import cartModel from "../models/Cart.js";  // Import mô hình Cart
import userModel from "../models/UserModel.js";  // Import mô hình User
import productModel from "../models/productModels.js";  // Import mô hình Product

const extractUserId = (req) => {
  return req.user?.uid || req.user?.id || req.body.userId;
};


// Thêm sản phẩm vào giỏ hàng
const addToCart = async (req, res) => {
  try {
    const { itemId, size, quantity, productData } = req.body;
    const userId = req.user?.uid || req.user?.id || req.body.userId;

    // 🔍 Lấy thông tin sản phẩm mới nhất từ database
    const product = await productModel.findById(itemId);
    if (!product) {
      return res.status(404).json({ success: false, message: "<PERSON>ản phẩm không tồn tại" });
    }

    // 🔍 Kiểm tra số lượng tồn kho
    if (product.quantity < quantity) {
      return res.status(400).json({
        success: false,
        message: `Không đủ hàng trong kho. Chỉ còn ${product.quantity} sản phẩm.`
      });
    }

    // Tìm giỏ hàng theo userId (firebaseUid)
    let cart = await cartModel.findOne({ userId });
    if (!cart) {
      cart = new cartModel({
        userId, // firebaseUid
        cartData: [{
            itemId,
          name: productData.name,
          image: productData.image,
          originalPrice: productData.originalPrice || productData.price,
          price: productData.price,
          hasPromotion: productData.hasPromotion || false,
          promotion: productData.promotion || null,
          discountPercentage: productData.promotion?.discountPercentage || 0,
          size: size,
          quantity: quantity,
          productQuantity: product.quantity // 🔄 Sử dụng số lượng tồn kho mới nhất
        }]
      });
    } else {
      let cartData = cart.cartData || [];
      const existingItemIndex = cartData.findIndex(item => item.itemId === itemId && item.size === size)

      if (existingItemIndex !== -1) {
        // 🔍 Kiểm tra tổng số lượng sau khi cộng dồn
        const newTotalQuantity = cartData[existingItemIndex].quantity + quantity;
        if (product.quantity < newTotalQuantity) {
          return res.status(400).json({
            success: false,
            message: `Không đủ hàng trong kho. Chỉ còn ${product.quantity} sản phẩm, bạn đã có ${cartData[existingItemIndex].quantity} trong giỏ hàng.`
          });
        }

        // Cộng dồn số lượng nếu sản phẩm đã có trong giỏ
        cartData[existingItemIndex].quantity += quantity;
        cartData[existingItemIndex].productQuantity = product.quantity; // 🔄 Cập nhật tồn kho mới nhất
      } else {
        // Thêm mới sản phẩm vào giỏ
        cartData.push({
          itemId,
          name: productData.name,
          image: productData.image,
          originalPrice: productData.originalPrice || productData.price,
          price: productData.price,
          hasPromotion: productData.hasPromotion || false,
          promotion: productData.promotion || null,
          discountPercentage: productData.promotion?.discountPercentage || 0,
          size: size,
          quantity: quantity,
          productQuantity: product.quantity // 🔄 Sử dụng số lượng tồn kho mới nhất
        });
      }

      cart.cartData = cartData;
    }

    await cart.save();
    res.json({ success: true, message: "Đã thêm vào giỏ hàng" });
    console.log("🛒 Add to cart: userId =", userId, "itemId =", itemId, "size =", size);


  } catch (error) {
    console.log(error);
    res.json({ success: false, message: "Lỗi khi thêm vào giỏ hàng" });
  }
};



const removeFromCart = async (req, res) => {
  try {
    const { itemId, size } = req.body;
    const userId = extractUserId(req);

    if (!userId) {
      return res.status(401).json({ success: false, message: "Không xác định được người dùng" });
    }

    let cart = await cartModel.findOne({ userId });
    if (!cart) {
      return res.json({ success: false, message: "Giỏ hàng không tồn tại" });
    }

    cart.cartData = cart.cartData.filter(item => !(item.itemId === itemId && item.size === size));

    await cart.save();
    res.json({ success: true, message: "Đã xóa khỏi giỏ hàng" });

  } catch (error) {
    console.log(error);
    res.json({ success: false, message: "Lỗi khi xóa khỏi giỏ hàng" });
  }
};



const updateCartQuantity = async (req, res) => {
  try {
    const { itemId, size, quantity } = req.body;
    const userId = extractUserId(req);

    if (!userId) {
      return res.status(401).json({ success: false, message: "Không xác định được người dùng" });
    }

    let cart = await cartModel.findOne({ userId });
    if (!cart) {
      return res.json({ success: false, message: "Giỏ hàng không tồn tại" });
    }

    const itemIndex = cart.cartData.findIndex(item => item.itemId === itemId && item.size === size)

    if (itemIndex !== -1) {
      if (quantity <= 0) {
        cart.cartData.splice(itemIndex, 1); // Xóa nếu = 0
      } else {
        // 🔍 Kiểm tra tồn kho trước khi cập nhật
        const product = await productModel.findById(itemId);
        if (!product) {
          return res.status(404).json({ success: false, message: "Sản phẩm không tồn tại" });
        }

        if (product.quantity < quantity) {
          return res.status(400).json({
            success: false,
            message: `Không đủ hàng trong kho. Chỉ còn ${product.quantity} sản phẩm.`
          });
        }

        cart.cartData[itemIndex].quantity = quantity;
        cart.cartData[itemIndex].productQuantity = product.quantity; // 🔄 Cập nhật tồn kho mới nhất
      }
      await cart.save();
      return res.json({ success: true, message: "Đã cập nhật giỏ hàng" });
    }

    res.json({ success: false, message: "Không tìm thấy sản phẩm trong giỏ hàng" });

  } catch (error) {
    console.error(error);
    res.json({ success: false, message: "Lỗi khi cập nhật giỏ hàng" });
  }
};



// Lấy giỏ hàng của user
const getCart = async (req, res) => {
  try {
    const { userId } = req.body;

    let cart = await cartModel.findOne({ userId });
    if (!cart) {
      return res.json({ success: false, message: "Giỏ hàng không tồn tại" });
    }

    // 🔄 Cập nhật thông tin tồn kho mới nhất cho từng sản phẩm trong giỏ hàng
    const updatedCartData = [];

    for (const item of cart.cartData) {
      try {
        // Lấy thông tin sản phẩm mới nhất từ database
        const product = await productModel.findById(item.itemId);
        if (product) {
          // Sản phẩm vẫn tồn tại, cập nhật thông tin tồn kho
          updatedCartData.push({
            ...item,
            productQuantity: product.quantity // Cập nhật số lượng tồn kho mới nhất
          });
        } else {
          // Sản phẩm không tồn tại nữa, ghi log nhưng vẫn giữ trong giỏ hàng với cảnh báo
          console.warn(`Product ${item.itemId} not found, marking as unavailable`);
          updatedCartData.push({
            ...item,
            productQuantity: 0, // Đánh dấu hết hàng
            isUnavailable: true // Đánh dấu không khả dụng
          });
        }
      } catch (error) {
        console.error(`Error updating stock for item ${item.itemId}:`, error);
        // Giữ nguyên item với thông tin cũ nếu có lỗi
        updatedCartData.push(item);
      }
    }

    res.json({ success: true, cartData: updatedCartData });

  } catch (error) {
    console.log(error);
    res.json({ success: false, message: "Lỗi khi lấy giỏ hàng" });
  }
};


// Đồng bộ giỏ hàng từ client lên server
const syncCart = async (req, res) => {
  try {
    const { userId, cartData } = req.body;

    // Kiểm tra xem người dùng có giỏ hàng hay không
    let cart = await cartModel.findOne({ userId });

    if (!cart) {
      // Nếu không có giỏ hàng, tạo mới giỏ hàng cho người dùng
      cart = new cartModel({
        userId,
        cartData: cartData || []
      });
    } else {
      // Nếu đã có giỏ hàng, cập nhật giỏ hàng
      cart.cartData = cartData || [];
    }

    // Lưu lại giỏ hàng
    await cart.save();
    res.json({ success: true, message: "Đã đồng bộ giỏ hàng" });

  } catch (error) {
    console.log(error);
    res.json({ success: false, message: "Lỗi khi đồng bộ giỏ hàng" });
  }
};


// Xóa toàn bộ giỏ hàng
const clearCart = async (req, res) => {
  try {
    const { userId } = req.body;

    // Kiểm tra xem người dùng có giỏ hàng hay không
    let cart = await cartModel.findOne({ userId });

    if (!cart) {
      return res.json({ success: false, message: "Giỏ hàng không tồn tại" });
    }

    // Xóa toàn bộ giỏ hàng
    cart.cartData = [];
    await cart.save();

    res.json({ success: true, message: "Đã xóa toàn bộ giỏ hàng" });

  } catch (error) {
    console.log(error);
    res.json({ success: false, message: "Lỗi khi xóa giỏ hàng" });
  }
};


export {
  addToCart,
  removeFromCart,
  updateCartQuantity,
  getCart,
  syncCart,
  clearCart
};
