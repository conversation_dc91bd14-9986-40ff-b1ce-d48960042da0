import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './CommentManagementPage.css';

const CommentManagementPage = () => {
  const [reviews, setReviews] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [editReview, setEditReview] = useState(null);
  const [editComment, setEditComment] = useState('');
  
  useEffect(() => {
    fetchReviews();
  }, []);

  const fetchReviews = async () => {
    try {
      const response = await axios.get('http://localhost:4000/api/admin/reviews');
      if (response.data.success) {
        setReviews(response.data.reviews);
      } else {
        setError('Không thể tải bình luận');
      }
    } catch (err) {
      setError('Lỗi khi tải bình luận');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteComment = async (reviewId) => {
    try {
      const response = await axios.delete(`http://localhost:4000/api/admin/review/${reviewId}`);
      if (response.data.success) {
        setReviews(reviews.filter(review => review._id !== reviewId));
        alert('Bình luận đã được xóa');
      }
    } catch (err) {
      alert('Lỗi khi xóa bình luận');
      console.error(err);
    }
  };

  const handleReportComment = async (reviewId) => {
    try {
      const response = await axios.post(`http://localhost:4000/api/review/report/${reviewId}`);
      if (response.data.success) {
        alert('Bình luận đã được báo cáo');
      }
    } catch (err) {
      alert('Lỗi khi báo cáo bình luận');
      console.error(err);
    }
  };

  const handleEditComment = (reviewId) => {
    const review = reviews.find(r => r._id === reviewId);
    setEditReview(review);
    setEditComment(review.comment);
  };

  const handleSaveEdit = async () => {
    try {
      const response = await axios.put('http://localhost:4000/api/review/edit', {
        reviewId: editReview._id,
        comment: editComment
      });

      if (response.data.success) {
        const updatedReviews = reviews.map(review =>
          review._id === editReview._id ? { ...review, comment: editComment } : review
        );
        setReviews(updatedReviews);
        setEditReview(null);
        setEditComment('');
        alert('Bình luận đã được cập nhật');
      }
    } catch (err) {
      alert('Lỗi khi chỉnh sửa bình luận');
      console.error(err);
    }
  };

  const renderStars = (rating) => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(i <= rating ? '⭐' : '☆');
    }
    return stars.join(' ');
  };

  if (loading) {
    return <div>Đang tải...</div>;
  }

  if (error) {
    return <div>{error}</div>;
  }

  return (
    <div className="comment-management">
      <h2>Quản lý bình luận</h2>
      <div className="review-list">
        {reviews.length === 0 ? (
          <p>Chưa có bình luận nào</p>
        ) : (
          reviews.map((review) => (
            <div key={review._id} className="review-item">
              <div className="review-header">
                <div className="review-user">
                  <strong>{review.userDisplayName || `Khách hàng ${review.userId.slice(-4)}`}</strong>
                  <p>{renderStars(review.rating)}</p>
                </div>
                <div className="review-actions">
                  <button onClick={() => handleEditComment(review._id)} className="edit-btn">Chỉnh sửa</button>
                  <button onClick={() => handleDeleteComment(review._id)} className="delete-btn">Xóa</button>
                  <button onClick={() => handleReportComment(review._id)} className="report-btn">Báo cáo</button>
                </div>
              </div>
              <p>{review.comment}</p>
              <p className="review-date">{new Date(review.date).toLocaleDateString()}</p>
            </div>
          ))
        )}
      </div>

      {/* Modal chỉnh sửa bình luận */}
      {editReview && (
        <div className="modal-overlay">
          <div className="modal">
            <h3>Chỉnh sửa bình luận</h3>
            <textarea
              value={editComment}
              onChange={(e) => setEditComment(e.target.value)}
            />
            <div className="modal-actions">
              <button onClick={handleSaveEdit} className="save-btn">Lưu</button>
              <button onClick={() => setEditReview(null)} className="cancel-btn">Hủy</button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CommentManagementPage;
